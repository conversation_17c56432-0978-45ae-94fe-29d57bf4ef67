# 🚀 نظام إدارة المعبر الحدودي - دليل البدء السريع

## 📋 ملخص المشروع

تم إنشاء نظام إدارة معبر حدودي متكامل باستخدام Python و PyQt5 و SQLite. النظام يدعم:

- ✅ واجهة عربية مع دعم RTL
- ✅ قاعدة بيانات SQLite محلية
- ✅ إدخال حركات الدخول/الخروج
- ✅ البحث والتصفية
- ✅ تصدير تقارير Excel
- ✅ بيانات تجريبية جاهزة

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام ملفات .bat (Windows)

1. **للتثبيت الأولي:**
   ```
   انقر مرتين على: install.bat
   ```

2. **لتشغيل التطبيق:**
   ```
   انقر مرتين على: run.bat
   ```

3. **لتشغيل التطبيق مع بيانات تجريبية:**
   ```
   انقر مرتين على: demo.bat
   ```

### الطريقة الثانية: استخدام سطر الأوامر

1. **تثبيت المتطلبات:**
   ```bash
   pip install PyQt5 pandas openpyxl
   ```

2. **تشغيل التطبيق:**
   ```bash
   python main.py
   ```

3. **إضافة بيانات تجريبية:**
   ```bash
   python add_sample_data.py
   ```

4. **فحص النظام:**
   ```bash
   python check_system.py
   ```

## 📁 هيكل المشروع

```
PMS/
├── 📄 main.py                    # الملف الرئيسي
├── 📄 database.py               # إدارة قاعدة البيانات
├── 📄 requirements.txt          # المكتبات المطلوبة
├── 📄 add_sample_data.py        # إضافة بيانات تجريبية
├── 📄 check_system.py           # فحص النظام
├── 📄 run.bat                   # تشغيل سريع
├── 📄 install.bat               # تثبيت المتطلبات
├── 📄 demo.bat                  # تشغيل مع بيانات تجريبية
├── 📂 ui/                       # واجهات المستخدم
│   ├── main_window.py           # النافذة الرئيسية
│   ├── movement_entry_widget.py # واجهة إدخال الحركة
│   └── movements_view_widget.py # واجهة عرض الحركات
├── 📂 models/                   # نماذج البيانات
│   └── movement_model.py        # نموذج الحركات
├── 📂 utils/                    # الأدوات المساعدة
│   └── excel_exporter.py        # تصدير Excel
├── 📂 reports/                  # مجلد التقارير
├── 📂 logs/                     # مجلد السجلات
└── 📂 backups/                  # مجلد النسخ الاحتياطية
```

## 🎯 الميزات الرئيسية

### 1. إدخال الحركات
- تسجيل حركات الدخول والخروج
- بيانات المركبة والسائق
- نقاط تفتيش متعددة
- بيانات إضافية اختيارية

### 2. عرض وإدارة الحركات
- جدول تفاعلي للحركات
- البحث برقم اللوحة
- التصفية بالتاريخ
- ترتيب البيانات

### 3. التقارير
- تصدير إلى Excel
- تقارير ملخصة
- إحصائيات نقاط التفتيش

## 🔧 استكشاف الأخطاء

### مشكلة: لا يعمل التطبيق
```bash
python check_system.py
```

### مشكلة: مكتبات مفقودة
```bash
pip install -r requirements.txt
```

### مشكلة: قاعدة البيانات
- احذف ملف `border_management.db`
- شغل التطبيق مرة أخيرى لإعادة إنشائها

## 📊 البيانات التجريبية

يتضمن النظام بيانات تجريبية تشمل:
- 5 مركبات مختلفة
- 5 سائقين
- 3 نقاط تفتيش
- حركات للأيام الثلاثة الماضية

## 🎨 الواجهة

- **دعم كامل للغة العربية**
- **اتجاه RTL**
- **تصميم حديث ومريح**
- **ألوان متناسقة**
- **سهولة الاستخدام**

## 📈 الإحصائيات الحالية

بعد إضافة البيانات التجريبية:
- ✅ 3 نقاط تفتيش
- ✅ 5 مركبات
- ✅ 5 أشخاص
- ✅ 21+ حركة

## 🔄 التحديثات المستقبلية

يمكن إضافة المزيد من الميزات مثل:
- تقارير PDF
- نسخ احتياطية تلقائية
- إعدادات النظام
- تسجيل دخول المستخدمين
- واجهة ويب

---

**🎉 النظام جاهز للاستخدام! ابدأ بتشغيل `run.bat` أو `python main.py`**
