from datetime import datetime
from database import Database

class MovementModel:
    def __init__(self):
        self.db = Database()
    
    def add_movement_entry(self, checkpoint_name, plate_number, driver_name, identity_number, 
                          direction, movement_time=None, vehicle_type="", model="", color="", 
                          nationality="", phone="", notes=""):
        """إضافة حركة جديدة مع إنشاء المركبة والشخص إذا لم يكونا موجودين"""
        
        if movement_time is None:
            movement_time = datetime.now()
        
        # البحث عن نقطة التفتيش
        checkpoints = self.db.get_checkpoints()
        checkpoint_id = None
        for cp in checkpoints:
            if cp['name'] == checkpoint_name:
                checkpoint_id = cp['id']
                break
        
        if checkpoint_id is None:
            # إنشاء نقطة تفتيش جديدة
            checkpoint_id = self.db.add_checkpoint(checkpoint_name)
        
        # البحث عن المركبة أو إنشاؤها
        vehicle = self.db.get_vehicle_by_plate(plate_number)
        if vehicle is None:
            vehicle_id = self.db.add_vehicle(plate_number, vehicle_type, model, color)
        else:
            vehicle_id = vehicle['id']
        
        # البحث عن الشخص أو إنشاؤه
        person = self.db.get_person_by_identity(identity_number)
        if person is None:
            person_id = self.db.add_person(identity_number, driver_name, nationality, phone)
        else:
            person_id = person['id']
        
        # إضافة الحركة
        movement_id = self.db.add_movement(
            checkpoint_id, vehicle_id, person_id, direction, movement_time, notes
        )
        
        return movement_id
    
    def get_movements_list(self, limit=100, offset=0, plate_filter="", date_filter=""):
        """الحصول على قائمة الحركات مع التصفية"""
        return self.db.get_movements(limit, offset, plate_filter, date_filter)
    
    def get_checkpoints_list(self):
        """الحصول على قائمة نقاط التفتيش"""
        return self.db.get_checkpoints()
    
    def search_by_plate(self, plate_number):
        """البحث عن حركات مركبة معينة"""
        return self.db.get_movements(plate_filter=plate_number)
    
    def search_by_date(self, date_str):
        """البحث عن حركات في تاريخ معين"""
        return self.db.get_movements(date_filter=date_str)
