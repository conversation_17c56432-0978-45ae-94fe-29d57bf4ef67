# نظام إدارة المعبر الحدودي

نظام إدارة معبر حدودي متكامل يعمل بدون إنترنت، مصمم لتسجيل ومتابعة حركات الدخول والخروج للمركبات والمسافرين عبر نقاط التفتيش المختلفة.

## المميزات

- **واجهة عربية**: دعم كامل للغة العربية مع اتجاه RTL
- **قاعدة بيانات محلية**: استخدام SQLite للعمل بدون إنترنت
- **إدخال الحركات**: تسجيل حركات الدخول والخروج مع بيانات المركبات والسائقين
- **البحث والتصفية**: البحث بواسطة رقم اللوحة أو التاريخ
- **تصدير التقارير**: تصدير البيانات إلى ملفات Excel
- **واجهة سهلة الاستخدام**: تصميم بسيط ومناسب للاستخدام اليومي

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.7 أو أحدث على جهازك.

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install PyQt5==5.15.10 pandas==2.1.4 openpyxl==3.1.2
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## هيكل المشروع

```
PMS/
├── main.py                 # الملف الرئيسي للتطبيق
├── database.py            # إدارة قاعدة البيانات
├── requirements.txt       # المكتبات المطلوبة
├── README.md             # ملف التوثيق
├── models/               # نماذج البيانات
│   ├── __init__.py
│   └── movement_model.py
├── ui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── main_window.py
│   ├── movement_entry_widget.py
│   └── movements_view_widget.py
├── utils/                # الأدوات المساعدة
│   ├── __init__.py
│   └── excel_exporter.py
├── reports/              # مجلد التقارير (يتم إنشاؤه تلقائياً)
├── logs/                 # مجلد السجلات (يتم إنشاؤه تلقائياً)
└── backups/              # مجلد النسخ الاحتياطية (يتم إنشاؤه تلقائياً)
```

## استخدام النظام

### 1. إدخال حركة جديدة
- اختر "إدخال حركة جديدة" من الشريط الجانبي
- املأ البيانات المطلوبة:
  - نقطة التفتيش
  - رقم اللوحة
  - اسم السائق
  - رقم الهوية
  - الاتجاه (دخول/خروج)
  - وقت الحركة
- املأ البيانات الإضافية (اختيارية):
  - نوع المركبة
  - موديل المركبة
  - لون المركبة
  - الجنسية
  - رقم الهاتف
  - ملاحظات
- اضغط "حفظ الحركة"

### 2. عرض الحركات
- اختر "عرض الحركات" من الشريط الجانبي
- استخدم خيارات البحث:
  - البحث برقم اللوحة
  - البحث بالتاريخ
- اضغط "بحث" لتطبيق المرشحات
- اضغط "مسح البحث" لإظهار جميع الحركات

### 3. تصدير التقارير
- من صفحة عرض الحركات، اضغط "تصدير إلى Excel" لتصدير البيانات الحالية
- اضغط "تصدير تقرير ملخص" لتصدير إحصائيات مجمعة
- ستجد الملفات في مجلد `reports/`

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **checkpoints**: نقاط التفتيش
- **vehicles**: بيانات المركبات
- **persons**: بيانات الأشخاص
- **movements**: سجل الحركات

## الدعم الفني

في حالة مواجهة أي مشاكل:

1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تأكد من وجود صلاحيات الكتابة في مجلد التطبيق
3. تحقق من ملفات السجلات في مجلد `logs/`

## الإصدار

الإصدار الحالي: 1.0

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
