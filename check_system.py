#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف للتحقق من حالة النظام وقاعدة البيانات
"""

import os
import sys
from database import Database
from models.movement_model import MovementModel

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    print("=== التحقق من المكتبات المطلوبة ===")
    
    required_modules = {
        'PyQt5': 'PyQt5',
        'pandas': 'pandas', 
        'openpyxl': 'openpyxl',
        'sqlite3': 'sqlite3'
    }
    
    missing = []
    for name, module in required_modules.items():
        try:
            __import__(module)
            print(f"✓ {name}: متوفر")
        except ImportError:
            print(f"✗ {name}: غير متوفر")
            missing.append(name)
    
    if missing:
        print(f"\nالمكتبات المفقودة: {', '.join(missing)}")
        print("قم بتشغيل: pip install " + " ".join(missing))
        return False
    else:
        print("\n✓ جميع المكتبات متوفرة")
        return True

def check_database():
    """التحقق من قاعدة البيانات"""
    print("\n=== التحقق من قاعدة البيانات ===")
    
    try:
        db = Database()
        
        # التحقق من الجداول
        conn = db.get_connection()
        cursor = conn.cursor()
        
        tables = ['checkpoints', 'vehicles', 'persons', 'movements']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✓ جدول {table}: {count} سجل")
        
        conn.close()
        print("✓ قاعدة البيانات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        return False

def check_directories():
    """التحقق من المجلدات المطلوبة"""
    print("\n=== التحقق من المجلدات ===")
    
    required_dirs = ['ui', 'models', 'utils', 'reports', 'logs', 'backups']
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ مجلد {directory}: موجود")
        else:
            print(f"✗ مجلد {directory}: غير موجود")
            try:
                os.makedirs(directory)
                print(f"  → تم إنشاء مجلد {directory}")
            except Exception as e:
                print(f"  → فشل في إنشاء مجلد {directory}: {e}")

def check_files():
    """التحقق من الملفات الأساسية"""
    print("\n=== التحقق من الملفات الأساسية ===")
    
    required_files = [
        'main.py',
        'database.py',
        'ui/main_window.py',
        'ui/movement_entry_widget.py',
        'ui/movements_view_widget.py',
        'models/movement_model.py',
        'utils/excel_exporter.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}: موجود")
        else:
            print(f"✗ {file_path}: غير موجود")

def test_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n=== اختبار الوظائف الأساسية ===")
    
    try:
        # اختبار نموذج الحركة
        model = MovementModel()
        
        # اختبار الحصول على نقاط التفتيش
        checkpoints = model.get_checkpoints_list()
        print(f"✓ نقاط التفتيش: {len(checkpoints)} نقطة")
        
        # اختبار الحصول على الحركات
        movements = model.get_movements_list(limit=10)
        print(f"✓ الحركات: {len(movements)} حركة")
        
        # اختبار تصدير Excel
        from utils.excel_exporter import ExcelExporter
        exporter = ExcelExporter()
        print("✓ مُصدِّر Excel: جاهز")
        
        print("✓ جميع الوظائف تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار الوظائف: {e}")
        return False

def main():
    """الدالة الرئيسية للفحص"""
    print("نظام إدارة المعبر الحدودي - فحص النظام")
    print("=" * 50)
    
    all_good = True
    
    # فحص المكتبات
    if not check_dependencies():
        all_good = False
    
    # فحص المجلدات
    check_directories()
    
    # فحص الملفات
    check_files()
    
    # فحص قاعدة البيانات
    if not check_database():
        all_good = False
    
    # اختبار الوظائف
    if not test_functionality():
        all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("✓ النظام جاهز للاستخدام!")
        print("يمكنك تشغيل التطبيق باستخدام: python main.py")
    else:
        print("✗ يوجد مشاكل في النظام")
        print("يرجى حل المشاكل المذكورة أعلاه")
    
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
