#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon
from ui.main_window import MainWindow

def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التطبيق للغة العربية (RTL)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين الخط الافتراضي للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # تعيين اسم التطبيق
    app.setApplicationName("نظام إدارة المعبر الحدودي")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Border Management System")
    
    # تطبيق نمط عام للتطبيق
    app.setStyleSheet("""
        QApplication {
            font-family: Arial, sans-serif;
        }
        
        QWidget {
            font-size: 12px;
        }
        
        QMessageBox {
            font-size: 12px;
        }
        
        QMessageBox QPushButton {
            min-width: 80px;
            padding: 5px 15px;
        }
    """)
    
    return app

def check_dependencies():
    """التحقق من وجود المكتبات المطلوبة"""
    missing_modules = []
    
    try:
        import PyQt5
    except ImportError:
        missing_modules.append("PyQt5")
    
    try:
        import pandas
    except ImportError:
        missing_modules.append("pandas")
    
    try:
        import openpyxl
    except ImportError:
        missing_modules.append("openpyxl")
    
    if missing_modules:
        error_msg = "المكتبات التالية مفقودة:\n"
        error_msg += "\n".join(f"- {module}" for module in missing_modules)
        error_msg += "\n\nيرجى تثبيتها باستخدام:\n"
        error_msg += "pip install " + " ".join(missing_modules)
        
        print(error_msg)
        return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ["reports", "logs", "backups"]
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"تم إنشاء مجلد: {directory}")
            except Exception as e:
                print(f"فشل في إنشاء مجلد {directory}: {e}")

def main():
    """الدالة الرئيسية للتطبيق"""
    
    # التحقق من المكتبات المطلوبة
    if not check_dependencies():
        input("اضغط Enter للخروج...")
        return 1
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    
    try:
        # إعداد التطبيق
        app = setup_application()
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # عرض النافذة
        main_window.show()
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        error_msg = f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}"
        print(error_msg)
        
        # محاولة عرض رسالة خطأ إذا كان PyQt5 متاحاً
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            QMessageBox.critical(None, "خطأ في التطبيق", error_msg)
        except:
            pass
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
