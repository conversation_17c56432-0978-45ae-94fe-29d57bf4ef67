from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBox<PERSON>ayout, QForm<PERSON>ayout,
                             QLineEdit, QComboBox, QPushButton, QDateTimeEdit,
                             QTextEdit, QLabel, QMessageBox, QGroupBox, QFrame,
                             QScrollArea, QGridLayout, QSizePolicy)
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QFont
from models.movement_model import MovementModel
from datetime import datetime

class MovementEntryWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.movement_model = MovementModel()
        self.setup_ui()
        self.load_checkpoints()
    
    def setup_ui(self):
        """إعداد واجهة إدخال الحركة"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("إدخال حركة جديدة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # إنشاء scroll area للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى قابل للتمرير
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)
        
        # مجموعة بيانات الحركة
        movement_group = QGroupBox("بيانات الحركة")
        movement_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # استخدام QGridLayout بدلاً من QFormLayout للمرونة
        movement_layout = QGridLayout(movement_group)
        movement_layout.setSpacing(15)
        movement_layout.setColumnStretch(1, 1)  # جعل العمود الثاني قابل للتمدد
        
        # إضافة الحقول مع تحسين المرونة
        row = 0
        
        # نقطة التفتيش
        checkpoint_label = QLabel("نقطة التفتيش:")
        self.checkpoint_combo = QComboBox()
        self.checkpoint_combo.setEditable(True)
        self.checkpoint_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        movement_layout.addWidget(checkpoint_label, row, 0)
        movement_layout.addWidget(self.checkpoint_combo, row, 1)
        row += 1
        
        # رقم اللوحة
        plate_label = QLabel("رقم اللوحة:")
        self.plate_input = QLineEdit()
        self.plate_input.setPlaceholderText("أدخل رقم اللوحة")
        self.plate_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        movement_layout.addWidget(plate_label, row, 0)
        movement_layout.addWidget(self.plate_input, row, 1)
        row += 1
        
        # باقي الحقول...
        self.setup_remaining_fields(movement_layout, row)
        
        scroll_layout.addWidget(movement_group)
        
        # إضافة المجموعة الإضافية
        self.setup_additional_group(scroll_layout)
        
        # إضافة الأزرار
        self.setup_buttons(scroll_layout)
        
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
    
    def setup_remaining_fields(self, layout, start_row):
        """إعداد باقي حقول البيانات الأساسية"""
        from PyQt5.QtWidgets import QSizePolicy
        
        row = start_row
        
        # اسم السائق
        driver_label = QLabel("اسم السائق:")
        self.driver_input = QLineEdit()
        self.driver_input.setPlaceholderText("أدخل اسم السائق")
        self.driver_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        layout.addWidget(driver_label, row, 0)
        layout.addWidget(self.driver_input, row, 1)
        row += 1
        
        # رقم الهوية
        identity_label = QLabel("رقم الهوية:")
        self.identity_input = QLineEdit()
        self.identity_input.setPlaceholderText("أدخل رقم الهوية")
        self.identity_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        layout.addWidget(identity_label, row, 0)
        layout.addWidget(self.identity_input, row, 1)
        row += 1
        
        # الاتجاه
        direction_label = QLabel("الاتجاه:")
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["دخول", "خروج"])
        self.direction_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        layout.addWidget(direction_label, row, 0)
        layout.addWidget(self.direction_combo, row, 1)
        row += 1
        
        # وقت الحركة
        datetime_label = QLabel("وقت الحركة:")
        self.datetime_input = QDateTimeEdit()
        self.datetime_input.setDateTime(QDateTime.currentDateTime())
        self.datetime_input.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.datetime_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        layout.addWidget(datetime_label, row, 0)
        layout.addWidget(self.datetime_input, row, 1)

    def setup_additional_group(self, parent_layout):
        """إعداد مجموعة البيانات الإضافية"""
        from PyQt5.QtWidgets import QSizePolicy
        
        additional_group = QGroupBox("بيانات إضافية (اختيارية)")
        additional_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        additional_layout = QGridLayout(additional_group)
        additional_layout.setSpacing(15)
        additional_layout.setColumnStretch(1, 1)
        
        # إضافة الحقول الإضافية
        fields = [
            ("نوع المركبة:", "vehicle_type_input", "سيارة، شاحنة، دراجة نارية..."),
            ("موديل المركبة:", "model_input", "تويوتا كامري، نيسان صني..."),
            ("لون المركبة:", "color_input", "أبيض، أسود، أزرق..."),
            ("الجنسية:", "nationality_input", "سعودي، مصري، أردني..."),
            ("رقم الهاتف:", "phone_input", "05xxxxxxxx")
        ]
        
        for row, (label_text, field_name, placeholder) in enumerate(fields):
            label = QLabel(label_text)
            field = QLineEdit()
            field.setPlaceholderText(placeholder)
            field.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            setattr(self, field_name, field)
            
            additional_layout.addWidget(label, row, 0)
            additional_layout.addWidget(field, row, 1)
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setPlaceholderText("أي ملاحظات إضافية...")
        self.notes_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        additional_layout.addWidget(notes_label, len(fields), 0)
        additional_layout.addWidget(self.notes_input, len(fields), 1)
        
        parent_layout.addWidget(additional_group)

    def setup_buttons(self, parent_layout):
        """إعداد أزرار العمليات"""
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ الحركة")
        self.save_button.clicked.connect(self.save_movement)
        self.save_button.setMinimumHeight(40)
        
        # زر المسح
        self.clear_button = QPushButton("مسح البيانات")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(40)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addStretch()
        
        parent_layout.addLayout(buttons_layout)
        parent_layout.addStretch()
    
    def apply_input_styles(self):
        """تطبيق الأنماط على حقول الإدخال"""
        input_style = """
            QLineEdit, QComboBox, QDateTimeEdit, QTextEdit {
                border: 2px solid #ced4da;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-height: 20px;
                max-height: none;
            }
            QLineEdit:focus, QComboBox:focus, QDateTimeEdit:focus, QTextEdit:focus {
                border-color: #007bff;
                outline: none;
                background-color: #f8f9ff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QTextEdit {
                min-height: 60px;
                max-height: 120px;
            }
        """
        
        # تطبيق الأنماط على جميع الحقول
        widgets = [
            self.plate_input, self.driver_input, self.identity_input,
            self.vehicle_type_input, self.model_input, self.color_input,
            self.nationality_input, self.phone_input, self.notes_input,
            self.checkpoint_combo, self.direction_combo, self.datetime_input
        ]
        
        for widget in widgets:
            widget.setStyleSheet(input_style)
            # تأكد من أن الحقول قابلة للتمدد
            if hasattr(widget, 'setSizePolicy'):
                from PyQt5.QtWidgets import QSizePolicy
                widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
    
    def load_checkpoints(self):
        """تحميل نقاط التفتيش"""
        checkpoints = self.movement_model.get_checkpoints_list()
        self.checkpoint_combo.clear()
        for checkpoint in checkpoints:
            self.checkpoint_combo.addItem(checkpoint['name'])
    
    def save_movement(self):
        """حفظ الحركة الجديدة"""
        # التحقق من البيانات المطلوبة
        if not self.plate_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم اللوحة")
            return
        
        if not self.driver_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم السائق")
            return
        
        if not self.identity_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهوية")
            return
        
        if not self.checkpoint_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار نقطة التفتيش")
            return
        
        try:
            # جمع البيانات
            movement_time = self.datetime_input.dateTime().toPyDateTime()
            
            # حفظ الحركة
            movement_id = self.movement_model.add_movement_entry(
                checkpoint_name=self.checkpoint_combo.currentText(),
                plate_number=self.plate_input.text().strip(),
                driver_name=self.driver_input.text().strip(),
                identity_number=self.identity_input.text().strip(),
                direction=self.direction_combo.currentText(),
                movement_time=movement_time,
                vehicle_type=self.vehicle_type_input.text().strip(),
                model=self.model_input.text().strip(),
                color=self.color_input.text().strip(),
                nationality=self.nationality_input.text().strip(),
                phone=self.phone_input.text().strip(),
                notes=self.notes_input.toPlainText().strip()
            )
            
            if movement_id:
                QMessageBox.information(self, "نجح", "تم حفظ الحركة بنجاح")
                self.clear_form()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حفظ الحركة")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
    
    def clear_form(self):
        """مسح جميع البيانات من النموذج"""
        self.plate_input.clear()
        self.driver_input.clear()
        self.identity_input.clear()
        self.vehicle_type_input.clear()
        self.model_input.clear()
        self.color_input.clear()
        self.nationality_input.clear()
        self.phone_input.clear()
        self.notes_input.clear()
        self.datetime_input.setDateTime(QDateTime.currentDateTime())
        self.direction_combo.setCurrentIndex(0)




