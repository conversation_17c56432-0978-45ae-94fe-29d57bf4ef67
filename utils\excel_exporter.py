import pandas as pd
from datetime import datetime
import os

class ExcelExporter:
    def __init__(self):
        pass
    
    def export_movements_to_excel(self, movements_data, filename=None):
        """تصدير الحركات إلى ملف Excel"""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"movements_report_{timestamp}.xlsx"
        
        # تحويل البيانات إلى DataFrame
        if not movements_data:
            # إنشاء DataFrame فارغ مع الأعمدة المطلوبة
            df = pd.DataFrame(columns=[
                'رقم الحركة', 'نقطة التفتيش', 'رقم اللوحة', 'اسم السائق', 
                'رقم الهوية', 'الاتجاه', 'وقت الحركة', 'ملاحظات'
            ])
        else:
            # تحويل البيانات إلى قائمة من القواميس
            data_list = []
            for movement in movements_data:
                data_list.append({
                    'رقم الحركة': movement['id'],
                    'نقطة التفتيش': movement['checkpoint_name'],
                    'رقم اللوحة': movement['plate_number'],
                    'اسم السائق': movement['person_name'],
                    'رقم الهوية': movement['identity_number'],
                    'الاتجاه': movement['direction'],
                    'وقت الحركة': movement['movement_time'],
                    'ملاحظات': movement['notes'] or ''
                })
            
            df = pd.DataFrame(data_list)
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        filepath = os.path.join(reports_dir, filename)
        
        # تصدير إلى Excel مع تنسيق عربي
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='تقرير الحركات', index=False)
            
            # الحصول على ورقة العمل لتطبيق التنسيق
            worksheet = writer.sheets['تقرير الحركات']
            
            # تعديل عرض الأعمدة
            column_widths = {
                'A': 15,  # رقم الحركة
                'B': 20,  # نقطة التفتيش
                'C': 15,  # رقم اللوحة
                'D': 25,  # اسم السائق
                'E': 20,  # رقم الهوية
                'F': 10,  # الاتجاه
                'G': 20,  # وقت الحركة
                'H': 30   # ملاحظات
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
        
        return filepath
    
    def export_summary_report(self, movements_data, filename=None):
        """تصدير تقرير ملخص إلى Excel"""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summary_report_{timestamp}.xlsx"
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        filepath = os.path.join(reports_dir, filename)
        
        if not movements_data:
            # إنشاء تقرير فارغ
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                empty_df = pd.DataFrame({'رسالة': ['لا توجد بيانات للتصدير']})
                empty_df.to_excel(writer, sheet_name='تقرير ملخص', index=False)
            return filepath
        
        # تحليل البيانات
        df = pd.DataFrame([
            {
                'checkpoint_name': m['checkpoint_name'],
                'direction': m['direction'],
                'movement_time': m['movement_time']
            } for m in movements_data
        ])
        
        # إحصائيات حسب نقطة التفتيش
        checkpoint_stats = df.groupby('checkpoint_name').size().reset_index(name='عدد الحركات')
        checkpoint_stats.columns = ['نقطة التفتيش', 'عدد الحركات']
        
        # إحصائيات حسب الاتجاه
        direction_stats = df.groupby('direction').size().reset_index(name='عدد الحركات')
        direction_stats.columns = ['الاتجاه', 'عدد الحركات']
        
        # تصدير إلى Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            checkpoint_stats.to_excel(writer, sheet_name='إحصائيات نقاط التفتيش', index=False)
            direction_stats.to_excel(writer, sheet_name='إحصائيات الاتجاهات', index=False)
            
            # تعديل عرض الأعمدة
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                for col in worksheet.columns:
                    max_length = 0
                    column = col[0].column_letter
                    for cell in col:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column].width = adjusted_width
        
        return filepath
