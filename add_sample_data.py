#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف لإضافة بيانات تجريبية للنظام
"""

from models.movement_model import MovementModel
from datetime import datetime, timedelta
import random

def add_sample_data():
    """إضافة بيانات تجريبية للنظام"""
    
    model = MovementModel()
    
    # بيانات تجريبية للمركبات والسائقين
    sample_data = [
        {
            "plate": "أ ب ج 1234",
            "driver": "أحمد محمد علي",
            "identity": "1234567890",
            "vehicle_type": "سيارة",
            "model": "تويوتا كامري",
            "color": "أبيض",
            "nationality": "سعودي",
            "phone": "0501234567"
        },
        {
            "plate": "د هـ و 5678",
            "driver": "فاطمة أحمد سالم",
            "identity": "2345678901",
            "vehicle_type": "سيارة",
            "model": "نيسان صني",
            "color": "أزرق",
            "nationality": "مصري",
            "phone": "0502345678"
        },
        {
            "plate": "ز ح ط 9012",
            "driver": "محمد عبدالله خالد",
            "identity": "3456789012",
            "vehicle_type": "شاحنة",
            "model": "إيسوزو",
            "color": "أحمر",
            "nationality": "أردني",
            "phone": "0503456789"
        },
        {
            "plate": "ي ك ل 3456",
            "driver": "سارة محمود حسن",
            "identity": "4567890123",
            "vehicle_type": "سيارة",
            "model": "هيونداي إلنترا",
            "color": "أسود",
            "nationality": "سعودي",
            "phone": "0504567890"
        },
        {
            "plate": "م ن س 7890",
            "driver": "عبدالرحمن صالح أحمد",
            "identity": "5678901234",
            "vehicle_type": "دراجة نارية",
            "model": "هوندا",
            "color": "أخضر",
            "nationality": "يمني",
            "phone": "0505678901"
        }
    ]
    
    checkpoints = ["البوابة الرئيسية", "البوابة الشمالية", "البوابة الجنوبية"]
    directions = ["دخول", "خروج"]
    
    print("إضافة بيانات تجريبية...")
    
    # إضافة حركات تجريبية للأيام الثلاثة الماضية
    for day_offset in range(3):
        date = datetime.now() - timedelta(days=day_offset)
        
        for i, data in enumerate(sample_data):
            # إضافة حركة دخول
            entry_time = date.replace(
                hour=random.randint(8, 12),
                minute=random.randint(0, 59),
                second=random.randint(0, 59)
            )
            
            movement_id = model.add_movement_entry(
                checkpoint_name=random.choice(checkpoints),
                plate_number=data["plate"],
                driver_name=data["driver"],
                identity_number=data["identity"],
                direction="دخول",
                movement_time=entry_time,
                vehicle_type=data["vehicle_type"],
                model=data["model"],
                color=data["color"],
                nationality=data["nationality"],
                phone=data["phone"],
                notes=f"حركة تجريبية - اليوم {day_offset + 1}"
            )
            
            if movement_id:
                print(f"تم إضافة حركة دخول للمركبة {data['plate']}")
            
            # إضافة حركة خروج (أحياناً)
            if random.choice([True, False]):
                exit_time = entry_time + timedelta(hours=random.randint(1, 8))
                
                movement_id = model.add_movement_entry(
                    checkpoint_name=random.choice(checkpoints),
                    plate_number=data["plate"],
                    driver_name=data["driver"],
                    identity_number=data["identity"],
                    direction="خروج",
                    movement_time=exit_time,
                    vehicle_type=data["vehicle_type"],
                    model=data["model"],
                    color=data["color"],
                    nationality=data["nationality"],
                    phone=data["phone"],
                    notes=f"حركة تجريبية - اليوم {day_offset + 1}"
                )
                
                if movement_id:
                    print(f"تم إضافة حركة خروج للمركبة {data['plate']}")
    
    print("\nتم الانتهاء من إضافة البيانات التجريبية!")
    print("يمكنك الآن تشغيل التطبيق ومشاهدة البيانات.")

if __name__ == "__main__":
    try:
        add_sample_data()
    except Exception as e:
        print(f"حدث خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")
